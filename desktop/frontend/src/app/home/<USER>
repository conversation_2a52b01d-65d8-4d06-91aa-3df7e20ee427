<!-- Dashboard Layout -->
<div
  class="page-container content-spacing"
  *ngIf="tabService.tabsValue.length === 0"
>
  <!-- Welcome State -->
  <mat-card class="card-spacing">
    <mat-card-header>
      <mat-card-title>Welcome to NS Drive Dashboard</mat-card-title>
      <mat-card-subtitle>
        Start by creating your first sync operation
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <p>
        Sync operations allow you to synchronize files between local directories
        and cloud storage services. Each operation runs independently with its
        own configuration and profile.
      </p>

      <mat-list>
        <mat-list-item>
          <mat-icon matListItemIcon color="primary">sync</mat-icon>
          <div matListItemTitle>Real-time synchronization</div>
        </mat-list-item>
        <mat-list-item>
          <mat-icon matListItemIcon color="primary">cloud</mat-icon>
          <div matListItemTitle>Multiple cloud providers</div>
        </mat-list-item>
        <mat-list-item>
          <mat-icon matListItemIcon color="primary">settings</mat-icon>
          <div matListItemTitle>Customizable profiles</div>
        </mat-list-item>
      </mat-list>
    </mat-card-content>

    <mat-card-actions align="end">
      <button mat-raised-button color="primary" (click)="createTab()">
        <mat-icon>add</mat-icon>
        Create First Operation
      </button>
    </mat-card-actions>
  </mat-card>
</div>

<!-- Operations Dashboard -->
<div
  *ngIf="tabService.tabsValue.length > 0"
  class="page-container content-spacing"
>
  <mat-card class="card-spacing">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>sync</mat-icon>
        <span>Sync Operations</span>
      </mat-card-title>
      <mat-card-subtitle>
        {{ tabService.tabsValue.length }} operation(s) configured
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content class="bg-transparent overflow-hidden">
      <!-- Tab Navigation -->
      <mat-tab-group
        [selectedIndex]="getActiveTabIndex()"
        (selectedIndexChange)="onTabChange($event)"
      >
        <mat-tab
          *ngFor="
            let tab of tabService.tabsValue;
            let i = index;
            trackBy: trackByTabId
          "
          class="bg-transparent overflow-hidden"
        >
          <ng-template mat-tab-label>
            <span>{{ tab?.name || "Operation " + (i + 1) }}</span>
            <button
              mat-icon-button
              [matMenuTriggerFor]="tabMenu"
              (click)="$event.stopPropagation(); setCurrentTabForMenu(tab.id)"
              *ngIf="tab && tab.id"
            >
              <mat-icon>more_vert</mat-icon>
            </button>
          </ng-template>

          <!-- Profile Selection -->
          <mat-form-field appearance="outline" class="form-spacing">
            <mat-label>Sync Profile</mat-label>
            <mat-select
              [value]="tab?.selectedProfileIndex"
              (selectionChange)="changeProfileTab($event.value, tab?.id)"
              [disabled]="!tab || !tab.id"
            >
              <mat-option [value]="null">
                <em>No profile selected</em>
              </mat-option>
              <mat-option
                *ngFor="
                  let profile of appService.configInfo$.value.profiles;
                  let idx = index
                "
                [value]="idx"
              >
                {{ profile.name }}
              </mat-option>
            </mat-select>
            <mat-icon matSuffix>folder_shared</mat-icon>
          </mat-form-field>

          <!-- Working Directory Section -->
          <div class="content-spacing">
            <div style="display: flex; align-items: center; margin-bottom: 8px">
              <mat-icon style="margin-right: 8px">folder</mat-icon>
              <span style="font-weight: 500">Working Directory</span>
            </div>
            <div
              style="
                background: var(--mat-app-surface-variant);
                padding: 16px;
                border-radius: 4px;
              "
            >
              <code>{{ (appService.configInfo$ | async)?.working_dir }}</code>
            </div>
          </div>

          <!-- Status Display -->
          <div *ngIf="tab.currentAction" class="content-spacing">
            <mat-progress-bar mode="indeterminate"></mat-progress-bar>
            <mat-chip-set>
              <mat-chip>
                <mat-icon matChipAvatar>{{
                  getActionIcon(tab.currentAction)
                }}</mat-icon>
                {{ getActionLabel(tab.currentAction) }}
              </mat-chip>
            </mat-chip-set>
          </div>

          <!-- Console Output -->
          <div class="content-spacing">
            <div style="display: flex; align-items: center; margin-bottom: 8px">
              <mat-icon style="margin-right: 8px">terminal</mat-icon>
              <span style="font-weight: 500">Console Output</span>
              <span class="spacer"></span>
              <button
                mat-icon-button
                (click)="clearTabOutput(tab.id)"
                matTooltip="Clear output"
              >
                <mat-icon>auto_delete</mat-icon>
              </button>
            </div>
            <pre
              style="
                background: var(--mat-app-surface-variant);
                padding: 16px;
                border-radius: 4px;
                max-height: 300px;
                overflow-y: auto;
              "
              >{{ tab.data.join("\n") || "No output yet..." }}</pre
            >
          </div>

          <!-- Action Buttons -->
          <div *ngIf="validateTabProfileIndex(tab)" class="content-spacing">
            <div style="display: flex; gap: 8px; flex-wrap: wrap">
              <button
                mat-raised-button
                [color]="tab.currentAction === Action.Pull ? 'warn' : 'primary'"
                [disabled]="
                  (!validateTabProfileIndex(tab) &&
                    tab.currentAction !== Action.Pull) ||
                  tab.isStopping
                "
                (click)="
                  tab.currentAction !== Action.Pull
                    ? pullTab(tab.id)
                    : stopCommandTab(tab.id)
                "
              >
                <mat-icon>{{
                  tab.isStopping
                    ? "hourglass_empty"
                    : tab.currentAction === Action.Pull
                    ? "stop"
                    : "download"
                }}</mat-icon>
                {{
                  tab.isStopping
                    ? "Stopping..."
                    : tab.currentAction === Action.Pull
                    ? "Stop Pull"
                    : "Pull"
                }}
              </button>

              <button
                mat-raised-button
                [color]="tab.currentAction === Action.Push ? 'warn' : 'accent'"
                [disabled]="
                  !validateTabProfileIndex(tab) &&
                  tab.currentAction !== Action.Push
                "
                (click)="
                  tab.currentAction !== Action.Push
                    ? pushTab(tab.id)
                    : stopCommandTab(tab.id)
                "
              >
                <mat-icon>{{
                  tab.currentAction === Action.Push ? "stop" : "upload"
                }}</mat-icon>
                {{ tab.currentAction === Action.Push ? "Stop Push" : "Push" }}
              </button>

              <button
                mat-raised-button
                [color]="tab.currentAction === Action.Bi ? 'warn' : 'primary'"
                [disabled]="
                  !validateTabProfileIndex(tab) &&
                  tab.currentAction !== Action.Bi
                "
                (click)="
                  tab.currentAction !== Action.Bi
                    ? biTab(tab.id)
                    : stopCommandTab(tab.id)
                "
              >
                <mat-icon>{{
                  tab.currentAction === Action.Bi ? "stop" : "sync"
                }}</mat-icon>
                {{ tab.currentAction === Action.Bi ? "Stop Sync" : "Sync" }}
              </button>

              <button
                mat-raised-button
                color="warn"
                [disabled]="
                  !validateTabProfileIndex(tab) &&
                  tab.currentAction !== Action.BiResync
                "
                (click)="
                  tab.currentAction !== Action.BiResync
                    ? biResyncTab(tab.id)
                    : stopCommandTab(tab.id)
                "
              >
                <mat-icon>{{
                  tab.currentAction === Action.BiResync ? "stop" : "refresh"
                }}</mat-icon>
                {{
                  tab.currentAction === Action.BiResync
                    ? "Stop Resync"
                    : "Resync"
                }}
              </button>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>

      <!-- Add Tab Button -->
      <div class="content-spacing" style="text-align: center">
        <button
          mat-raised-button
          color="primary"
          (click)="createTab()"
          style="margin-top: 16px"
        >
          <mat-icon>add</mat-icon>
          Add New Operation
        </button>
      </div>
    </mat-card-content>
  </mat-card>
</div>

<!-- Shared Tab Menu -->
<mat-menu #tabMenu="matMenu">
  <button mat-menu-item (click)="startRenameTab(currentTabIdForMenu)">
    <mat-icon>edit</mat-icon>
    <span>Rename</span>
  </button>
  <button mat-menu-item (click)="deleteTab(currentTabIdForMenu)">
    <mat-icon>delete</mat-icon>
    <span>Delete</span>
  </button>
</mat-menu>

<!-- Rename Tab Dialog -->
<ng-template #renameDialog>
  <h2 mat-dialog-title>Rename Tab</h2>
  <mat-dialog-content>
    <mat-form-field appearance="outline" style="width: 100%">
      <mat-label>Tab Name</mat-label>
      <input
        matInput
        [(ngModel)]="renameDialogData.newName"
        (keydown.enter)="confirmRename()"
        id="renameDialogInput"
        #renameDialogInput
      />
    </mat-form-field>
  </mat-dialog-content>
  <mat-dialog-actions align="end">
    <button mat-button (click)="cancelRename()">Cancel</button>
    <button mat-raised-button color="primary" (click)="confirmRename()">
      Rename
    </button>
  </mat-dialog-actions>
</ng-template>
