<!-- Dashboard Layout -->
<div
  class="p-6 max-w-4xl mx-auto space-y-6"
  *ngIf="tabService.tabsValue.length === 0"
>
  <!-- Welcome State -->
  <div class="card">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
        Welcome to NS Drive Dashboard
      </h1>
      <p class="text-gray-600 dark:text-gray-400">
        Start by creating your first sync operation
      </p>
    </div>

    <div class="mb-6">
      <p class="text-gray-700 dark:text-gray-300 mb-6">
        Sync operations allow you to synchronize files between local directories
        and cloud storage services. Each operation runs independently with its
        own configuration and profile.
      </p>

      <div class="space-y-4">
        <div class="flex items-center space-x-3">
          <svg
            class="w-6 h-6 text-primary-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            ></path>
          </svg>
          <span class="text-gray-700 dark:text-gray-300"
            >Real-time synchronization</span
          >
        </div>
        <div class="flex items-center space-x-3">
          <svg
            class="w-6 h-6 text-primary-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
            ></path>
          </svg>
          <span class="text-gray-700 dark:text-gray-300"
            >Multiple cloud providers</span
          >
        </div>
        <div class="flex items-center space-x-3">
          <svg
            class="w-6 h-6 text-primary-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
            ></path>
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
            ></path>
          </svg>
          <span class="text-gray-700 dark:text-gray-300"
            >Customizable profiles</span
          >
        </div>
      </div>
    </div>

    <div class="flex justify-end">
      <button class="btn-primary" (click)="createTab()">
        <svg
          class="w-5 h-5 mr-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          ></path>
        </svg>
        Create First Operation
      </button>
    </div>
  </div>
</div>

<!-- Operations Dashboard -->
<div *ngIf="tabService.tabsValue.length > 0" class="p-6 max-w-6xl mx-auto">
  <div class="card">
    <div class="mb-6">
      <div class="flex items-center space-x-3 mb-2">
        <svg
          class="w-6 h-6 text-primary-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          ></path>
        </svg>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
          Sync Operations
        </h1>
      </div>
      <p class="text-gray-600 dark:text-gray-400">
        {{ tabService.tabsValue.length }} operation(s) configured
      </p>
    </div>

    <!-- Tab Navigation -->
    <div class="border-b border-gray-200 dark:border-gray-700 mb-6">
      <nav class="flex space-x-8 overflow-x-auto">
        <button
          *ngFor="
            let tab of tabService.tabsValue;
            let i = index;
            trackBy: trackByTabId
          "
          [class]="
            getActiveTabIndex() === i ? 'tab-button-active' : 'tab-button'
          "
          (click)="onTabChange(i)"
        >
          <div class="flex items-center space-x-2">
            <span class="whitespace-nowrap">{{
              tab?.name || "Operation " + (i + 1)
            }}</span>
            <div class="flex items-center space-x-1 ml-2" *ngIf="tab && tab.id">
              <!-- Rename Button -->
              <button
                class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                (click)="$event.stopPropagation(); startRenameTab(tab.id)"
                title="Rename tab"
              >
                <svg
                  class="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                  ></path>
                </svg>
              </button>
              <!-- Delete Button -->
              <button
                class="p-1 rounded hover:bg-red-100 dark:hover:bg-red-900 text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
                (click)="$event.stopPropagation(); deleteTab(tab.id)"
                title="Delete tab"
              >
                <svg
                  class="w-4 h-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div
      *ngFor="
        let tab of tabService.tabsValue;
        let i = index;
        trackBy: trackByTabId
      "
    >
      <div *ngIf="getActiveTabIndex() === i" class="space-y-6">
        <!-- Profile Selection -->
        <div>
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >
            Sync Profile
          </label>
          <div class="relative">
            <select
              class="select-field pr-10"
              [value]="tab?.selectedProfileIndex"
              (change)="onProfileChange($event, tab?.id)"
              [disabled]="!tab || !tab.id"
            >
              <option [value]="null">No profile selected</option>
              <option
                *ngFor="
                  let profile of appService.configInfo$.value.profiles;
                  let idx = index
                "
                [value]="idx"
              >
                {{ profile.name }}
              </option>
            </select>
            <div
              class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none"
            >
              <svg
                class="w-5 h-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                ></path>
              </svg>
            </div>
          </div>
        </div>

        <!-- Working Directory Section -->
        <div>
          <div class="flex items-center space-x-2 mb-3">
            <svg
              class="w-5 h-5 text-gray-600 dark:text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
              ></path>
            </svg>
            <span class="font-medium text-gray-700 dark:text-gray-300"
              >Working Directory</span
            >
          </div>
          <div class="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <code class="text-sm text-gray-800 dark:text-gray-200">{{
              (appService.configInfo$ | async)?.working_dir
            }}</code>
          </div>
        </div>

        <!-- Status Display -->
        <div *ngIf="tab.currentAction">
          <div
            class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4"
          >
            <div
              class="bg-primary-600 h-2 rounded-full animate-pulse"
              style="width: 100%"
            ></div>
          </div>
          <div
            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200"
          >
            <svg
              class="w-4 h-4 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                [attr.d]="getActionIconPath(tab.currentAction)"
              ></path>
            </svg>
            {{ getActionLabel(tab.currentAction) }}
          </div>
        </div>

        <!-- Console Output -->
        <div>
          <div class="flex items-center justify-between mb-3">
            <div class="flex items-center space-x-2">
              <svg
                class="w-5 h-5 text-gray-600 dark:text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                ></path>
              </svg>
              <span class="font-medium text-gray-700 dark:text-gray-300"
                >Console Output</span
              >
            </div>
            <button
              class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              (click)="clearTabOutput(tab.id)"
              title="Clear output"
            >
              <svg
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                ></path>
              </svg>
            </button>
          </div>
          <pre class="console-output">{{
            tab.data.join("\n") || "No output yet..."
          }}</pre>
        </div>

        <!-- Action Buttons -->
        <div *ngIf="validateTabProfileIndex(tab)">
          <div class="flex flex-wrap gap-3">
            <button
              [class]="
                tab.currentAction === Action.Pull
                  ? 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap'
                  : 'btn-primary'
              "
              [disabled]="
                (!validateTabProfileIndex(tab) &&
                  tab.currentAction !== Action.Pull) ||
                tab.isStopping
              "
              (click)="
                tab.currentAction !== Action.Pull
                  ? pullTab(tab.id)
                  : stopCommandTab(tab.id)
              "
            >
              <svg
                class="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  [attr.d]="
                    tab.isStopping
                      ? 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'
                      : tab.currentAction === Action.Pull
                      ? 'M21 12a9 9 0 11-6.219-8.56'
                      : 'M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10'
                  "
                ></path>
              </svg>
              {{
                tab.isStopping
                  ? "Stopping..."
                  : tab.currentAction === Action.Pull
                  ? "Stop Pull"
                  : "Pull"
              }}
            </button>

            <button
              [class]="
                tab.currentAction === Action.Push
                  ? 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap'
                  : 'bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap'
              "
              [disabled]="
                !validateTabProfileIndex(tab) &&
                tab.currentAction !== Action.Push
              "
              (click)="
                tab.currentAction !== Action.Push
                  ? pushTab(tab.id)
                  : stopCommandTab(tab.id)
              "
            >
              <svg
                class="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  [attr.d]="
                    tab.currentAction === Action.Push
                      ? 'M21 12a9 9 0 11-6.219-8.56'
                      : 'M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12'
                  "
                ></path>
              </svg>
              {{ tab.currentAction === Action.Push ? "Stop Push" : "Push" }}
            </button>

            <button
              [class]="
                tab.currentAction === Action.Bi
                  ? 'bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap'
                  : 'btn-primary'
              "
              [disabled]="
                !validateTabProfileIndex(tab) && tab.currentAction !== Action.Bi
              "
              (click)="
                tab.currentAction !== Action.Bi
                  ? biTab(tab.id)
                  : stopCommandTab(tab.id)
              "
            >
              <svg
                class="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  [attr.d]="
                    tab.currentAction === Action.Bi
                      ? 'M21 12a9 9 0 11-6.219-8.56'
                      : 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
                  "
                ></path>
              </svg>
              {{ tab.currentAction === Action.Bi ? "Stop Sync" : "Sync" }}
            </button>

            <button
              class="bg-orange-600 hover:bg-orange-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 whitespace-nowrap"
              [disabled]="
                !validateTabProfileIndex(tab) &&
                tab.currentAction !== Action.BiResync
              "
              (click)="
                tab.currentAction !== Action.BiResync
                  ? biResyncTab(tab.id)
                  : stopCommandTab(tab.id)
              "
            >
              <svg
                class="w-5 h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  [attr.d]="
                    tab.currentAction === Action.BiResync
                      ? 'M21 12a9 9 0 11-6.219-8.56'
                      : 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'
                  "
                ></path>
              </svg>
              {{
                tab.currentAction === Action.BiResync ? "Stop Resync" : "Resync"
              }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add Tab Button -->
    <div class="text-center mt-6">
      <button class="btn-primary" (click)="createTab()">
        <svg
          class="w-5 h-5 mr-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          ></path>
        </svg>
        Add New Operation
      </button>
    </div>
  </div>
</div>

<!-- Rename Tab Modal -->
<div
  *ngIf="showRenameDialog"
  class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
  (click)="cancelRename()"
>
  <div
    class="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-md mx-4"
    (click)="$event.stopPropagation()"
  >
    <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-4">
      Rename Tab
    </h2>

    <div class="mb-6">
      <label
        class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
      >
        Tab Name
      </label>
      <input
        type="text"
        class="input-field"
        [(ngModel)]="renameDialogData.newName"
        (keydown.enter)="confirmRename()"
        #renameDialogInput
      />
    </div>

    <div class="flex justify-end space-x-3">
      <button class="btn-secondary" (click)="cancelRename()">Cancel</button>
      <button class="btn-primary" (click)="confirmRename()">Rename</button>
    </div>
  </div>
</div>
