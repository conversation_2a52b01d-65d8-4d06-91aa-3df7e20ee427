/* Home component specific styles (permanent dark mode) */

.mat-mdc-list-item .mat-icon[color="primary"] {
  color: #64b5f6 !important; /* Lighter blue for dark mode */
}

/* Fix for matListItemLine visibility in profile information display */
.mat-mdc-list-item .mat-mdc-list-item-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  flex: 1 !important;
}

.mat-mdc-list-item .mat-mdc-list-item-title {
  color: var(--primary-text) !important;
  font-weight: 500 !important;
  margin-bottom: 4px !important;
}

.mat-mdc-list-item .mat-mdc-list-item-line {
  color: var(--secondary-text) !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  display: block !important;
  width: 100% !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure profile information list items have proper layout */
.mat-mdc-list-item {
  min-height: 64px !important;
  padding: 12px 16px !important;
}

/* Override any global styles that might hide the line content */
.mat-mdc-list-item [matListItemLine] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  color: var(--secondary-text) !important;
  font-size: 14px !important;
  margin-top: 4px !important;
}

/* Specific styling for profile information items */
.profile-info-item {
  display: flex !important;
  align-items: flex-start !important;
  padding: 12px 16px !important;
  min-height: 64px !important;
}

.profile-info-item .mat-mdc-list-item-content {
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
  flex: 1 !important;
}

.profile-info-item .mat-mdc-list-item-title {
  color: var(--primary-text) !important;
  font-weight: 500 !important;
  margin-bottom: 4px !important;
  display: block !important;
}

.profile-info-item .mat-mdc-list-item-line {
  color: var(--secondary-text) !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  display: block !important;
  width: 100% !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.profile-info-item .mat-mdc-list-item-icon {
  margin-right: 16px !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  align-self: flex-start !important;
  margin-top: 4px !important;
}

/* Card avatar icon */
.mat-mdc-card-header .mat-icon {
  color: #64b5f6 !important;
}

/* Consistent spacing for operation controls */
.page-container {
  padding: 16px;
}

.card-spacing {
  margin-bottom: 16px;
}

/* Form field spacing */
.form-spacing {
  width: 100%;
  margin-bottom: 16px;
}

/* Console output styling */
pre {
  background-color: rgba(0, 0, 0, 0.2);
  padding: 12px;
  border-radius: 4px;
  font-family: "Courier New", monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 300px;
  overflow-y: auto;
  color: var(--primary-text) !important;
}
