.fab-button {
  position: fixed;
  bottom: 80px; /* Above bottom tabs */
  right: 16px;
  z-index: 1000;
}

.profile-item {
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex !important;
  align-items: center !important;
  padding: 12px 16px !important;
  min-height: 72px !important;
}

/* Ensure consistent icon alignment */
.profile-item .mat-mdc-list-item-icon {
  margin-right: 16px !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Ensure consistent content layout */
.profile-item .mat-mdc-list-item-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  align-items: flex-start !important;
}

/* Ensure consistent meta alignment */
.profile-item .mat-mdc-list-item-meta {
  margin-left: auto !important;
  display: flex !important;
  align-items: center !important;
}

/* Ensure list item lines are visible */
.profile-item .mat-mdc-list-item-title {
  color: var(--primary-text) !important;
  font-weight: 500 !important;
  margin-bottom: 4px !important;
}

.profile-item .mat-mdc-list-item-line {
  color: var(--secondary-text) !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}
