<!-- Profiles List -->
<div class="p-6 max-w-4xl mx-auto">
  <div
    *ngIf="(appService.configInfo$ | async)?.profiles?.length; else noProfiles"
    class="card"
  >
    <div class="mb-6">
      <div class="flex items-center space-x-3 mb-2">
        <lucide-icon
          [img]="UsersIcon"
          class="w-6 h-6 text-primary-600"
        ></lucide-icon>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-gray-100">
          Sync Profiles
        </h1>
      </div>
      <p class="text-gray-600 dark:text-gray-400">
        {{ (appService.configInfo$ | async)?.profiles?.length }} profile(s)
        configured
      </p>
    </div>

    <div class="space-y-3">
      <div
        *ngFor="
          let profile of (appService.configInfo$ | async)?.profiles;
          let idx = index
        "
        (click)="editProfile(idx)"
        class="flex items-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer transition-colors duration-200 group"
      >
        <div class="flex-shrink-0 mr-4">
          <svg
            class="w-8 h-8 text-gray-600 dark:text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
            ></path>
          </svg>
        </div>
        <div class="flex-1 min-w-0">
          <h3
            class="text-lg font-medium text-gray-900 dark:text-gray-100 truncate"
          >
            {{ profile.name || "Untitled Profile" }}
          </h3>
          <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {{ getProfileDescription(profile) }}
          </p>
        </div>
        <div class="flex-shrink-0 ml-4">
          <button
            (click)="removeProfile(idx); $event.stopPropagation()"
            class="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-100 dark:hover:bg-red-900 rounded-lg transition-colors duration-200 opacity-0 group-hover:opacity-100"
            title="Delete profile"
          >
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
              ></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- No Profiles State -->
<ng-template #noProfiles>
  <div class="p-6 max-w-4xl mx-auto">
    <div class="card text-center">
      <div class="mb-6">
        <div class="flex justify-center mb-4">
          <div
            class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center"
          >
            <svg
              class="w-8 h-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
              ></path>
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </div>
        </div>
        <h2 class="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
          No Profiles Found
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          Create your first sync profile to get started
        </p>
      </div>
      <div>
        <p class="text-gray-700 dark:text-gray-300 mb-6">
          Profiles allow you to configure different sync settings for various
          directories and remotes.
        </p>
        <button class="btn-primary" (click)="addProfile()">
          <lucide-icon [img]="PlusIcon" class="w-5 h-5 mr-2"></lucide-icon>
          Create First Profile
        </button>
      </div>
    </div>
  </div>
</ng-template>

<!-- Floating Action Button -->
<button
  (click)="addProfile()"
  class="fixed bottom-6 right-6 w-14 h-14 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center z-50"
  title="Add Profile"
>
  <lucide-icon [img]="PlusIcon" class="w-6 h-6"></lucide-icon>
</button>
