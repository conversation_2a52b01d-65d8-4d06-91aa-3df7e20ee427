.fab-button {
  position: fixed;
  bottom: 80px; /* Above bottom tabs */
  right: 16px;
  z-index: 1000;
}

/* Ensure consistent list item styling to match profiles */
.mat-mdc-list-item {
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex !important;
  align-items: center !important;
  padding: 12px 16px !important;
  min-height: 64px !important;
}

/* Ensure consistent icon alignment */
.mat-mdc-list-item .mat-mdc-list-item-icon {
  margin-right: 16px !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Ensure consistent content layout */
.mat-mdc-list-item .mat-mdc-list-item-content {
  display: flex !important;
  flex-direction: column !important;
  flex: 1 !important;
  align-items: flex-start !important;
}

/* Ensure consistent meta alignment */
.mat-mdc-list-item .mat-mdc-list-item-meta {
  margin-left: auto !important;
  display: flex !important;
  align-items: center !important;
}
