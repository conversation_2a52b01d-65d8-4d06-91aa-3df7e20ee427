@import "@angular/material/prebuilt-themes/azure-blue.css";

// Dark theme variables
:root {
  --background-color: #121212;
  --surface-color: #1e1e1e;
  --primary-text: rgba(255, 255, 255, 0.87);
  --secondary-text: rgba(255, 255, 255, 0.6);
  --border-color: rgba(255, 255, 255, 0.12);
  --hover-color: rgba(255, 255, 255, 0.08);
  --card-background: #1e1e1e;
  --toolbar-background: #1976d2;
  --toolbar-text: #fff;
  --button-text: #64b5f6;
  --icon-color: rgba(255, 255, 255, 0.6);
}

// Global styles
* {
  box-sizing: border-box;
  transition: background-color 0.3s, color 0.3s, border-color 0.3s;
  color: var(--primary-text) !important;
}
html,
body {
  height: 100%;
  margin: 0;
  font-family: "Roboto", "Helvetica Neue", sans-serif;
  background: var(--background-color) !important;
  color: var(--primary-text) !important;
}
.spacer {
  flex: 1 1 auto;
}
.page-container {
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
}
.content-spacing > * + * {
  margin-top: 16px;
}
.card-spacing {
  margin-bottom: 16px;
}
.form-spacing mat-form-field {
  width: 100%;
  margin-bottom: 16px;
}
.section-spacing {
  margin: 24px 0;
}
.tight-spacing > * + * {
  margin-top: 8px;
}

// Material overrides
.mat-mdc-card,
.mat-mdc-dialog-surface,
.mat-mdc-menu-panel,
.mat-mdc-select-panel,
.mat-mdc-snack-bar-container,
.mat-mdc-tooltip {
  background: var(--surface-color) !important;
  color: var(--primary-text) !important;
}
.mat-mdc-card {
  background: var(--card-background) !important;
  border: 1px solid var(--border-color) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}
.mat-toolbar {
  background: var(--toolbar-background) !important;
  color: var(--toolbar-text) !important;
}
.mat-mdc-list-item,
.mat-mdc-list-item-title,
.mat-mdc-list-item-line,
.mat-mdc-list-item-meta,
.mat-mdc-list-item-content,
.mat-mdc-list-base {
  color: var(--primary-text) !important;
  background: transparent !important;
}
.mat-mdc-list-item:hover,
.mat-mdc-menu-item:hover,
.mat-mdc-option:hover,
.mat-mdc-option.mat-mdc-option-active {
  background: var(--hover-color) !important;
}
.mat-mdc-list-item:not(:last-child) {
  border-bottom: 1px solid var(--border-color) !important;
}
.mat-mdc-card-title,
.mat-mdc-card-content,
.mat-mdc-tab-label-content,
.mat-mdc-tab-body-content,
.mat-mdc-tab-body,
.mat-mdc-tab-group {
  color: var(--primary-text) !important;
}
.mat-mdc-card-subtitle,
.mat-mdc-form-field .mat-mdc-form-field-label {
  color: var(--secondary-text) !important;
}
.mat-mdc-form-field.mat-focused .mat-mdc-form-field-label,
.mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline-thick {
  color: var(--button-text) !important;
}
.mat-mdc-form-field,
.mat-mdc-input-element,
.mat-mdc-select-value,
.mat-mdc-form-field-outline {
  color: var(--primary-text) !important;
}
.mat-mdc-button,
.mat-mdc-button.mat-primary {
  color: var(--button-text) !important;
}
.mat-mdc-raised-button,
.mat-mdc-raised-button.mat-primary,
.mat-mdc-fab,
.mat-mdc-card-header [mat-card-avatar] {
  background: #1976d2 !important;
  color: #fff !important;
}
.mat-mdc-icon-button,
.mat-icon {
  color: var(--icon-color) !important;
}
.mat-divider {
  border-top-color: var(--border-color) !important;
}

// Inputs, selects, code, etc.
input,
textarea,
select,
.mat-mdc-input,
.mat-mdc-select-trigger,
.mat-mdc-select-value-text,
pre,
code,
.console-output,
.code-block {
  color: var(--primary-text) !important;
}
::placeholder,
::-webkit-input-placeholder,
::-moz-placeholder,
:-ms-input-placeholder {
  color: var(--secondary-text) !important;
  opacity: 1;
}

// Headings and text
h1,
h2,
h3,
h4,
h5,
h6,
p,
span,
div,
label,
legend,
fieldset,
button,
a,
strong,
em,
small,
mark,
del,
ins,
sub,
sup,
blockquote,
cite,
q,
abbr,
time,
address,
details,
summary {
  color: var(--primary-text) !important;
}
